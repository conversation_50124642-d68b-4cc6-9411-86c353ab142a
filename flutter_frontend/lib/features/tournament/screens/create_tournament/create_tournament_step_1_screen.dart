// soccer_frontend/features/tournament/presentation/screens/create_tournament_step_1_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:soccer_frontend/features/tournament/blocs/create_tournament/create_tournament_bloc.dart';
import 'package:soccer_frontend/features/tournament/blocs/create_tournament/create_tournament_event.dart';
import 'package:soccer_frontend/features/tournament/blocs/create_tournament/create_tournament_state.dart';
import 'package:soccer_frontend/data/models/tournament.dart';
import 'package:soccer_frontend/features/auth/blocs/auth_bloc.dart';

class CreateTournamentStep1Screen extends StatefulWidget {
  const CreateTournamentStep1Screen({super.key});
  @override
  State<CreateTournamentStep1Screen> createState() =>
      _CreateTournamentStep1ScreenState();
}

class _CreateTournamentStep1ScreenState
    extends State<CreateTournamentStep1Screen> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _tournamentNameController;
  late TextEditingController _cityController;
  late TextEditingController _gamesPerTeamController; // Moved to be overall tournament
  late TextEditingController _rulesController;
  late TextEditingController _refundPolicyController;
  late TextEditingController _directorNameController;
  late TextEditingController _directorEmailController;
  late TextEditingController _directorPhoneController;

  DateTime? _startDate;
  DateTime? _endDate;
  DateTime? _registrationDeadline;
  String? _selectedState;
  String? _selectedTournamentFormat;

  // Tournament format options (keep existing)
  final List<String> _tournamentFormats = [
    'Sanctioned Tournament',
    'Championship Series Tournament',
    'School or Collegiate Tournament',
    'Invitational Tournament',
    'Unsanctioned Tournament',
  ];

  // List of US states for dropdown (keep existing)
  final List<String> _states = [
    'Alabama', 'Alaska', 'Arizona', 'Arkansas', 'California', 'Colorado',
    'Connecticut', 'Delaware', 'Florida', 'Georgia', 'Hawaii', 'Idaho',
    'Illinois', 'Indiana', 'Iowa', 'Kansas', 'Kentucky', 'Louisiana',
    'Maine', 'Maryland', 'Massachusetts', 'Michigan', 'Minnesota',
    'Mississippi', 'Missouri', 'Montana', 'Nebraska', 'Nevada',
    'New Hampshire', 'New Jersey', 'New Mexico', 'New York',
    'North Carolina', 'North Dakota', 'Ohio', 'Oklahoma', 'Oregon',
    'Pennsylvania', 'Rhode Island', 'South Carolina', 'South Dakota',
    'Tennessee', 'Texas', 'Utah', 'Vermont', 'Virginia', 'Washington',
    'West Virginia', 'Wisconsin', 'Wyoming'
  ];

  // NEW: State for Field Size -> Age Group -> Fee
  final List<String> _allPlayFormats = ['4v4', '7v7', '9v9', '11v11'];
  final List<String> _allAgeGroups = ['U6', 'U7', 'U8', 'U9', 'U10', 'U11', 'U12', 'U13', 'U14', 'U15', 'U16', 'U17', 'U18', 'U19', 'Adult'];

  // Tracks which play formats (field sizes) are offered
  Map<String, bool> _selectedPlayFormats = {
    '4v4': false,
    '7v7': false,
    '9v9': false,
    '11v11': false,
  };

  // Tracks selected age groups under each play format: Map<PlayFormat, Set<AgeGroup>>
  Map<String, Set<String>> _selectedAgeGroupsByPlayFormat = {};

  // Tracks fees at field size level: Map<PlayFormat, TextEditingController>
  Map<String, TextEditingController> _fieldSizeFeeControllers = {};

  // Early Bird and Late Fee settings (moved from Additional Info)
  DateTime? _earlyBirdDeadline;
  late TextEditingController _earlyBirdDiscountController;
  DateTime? _lateRegistrationStartDate;
  late TextEditingController _lateFeeAmountController;

  @override
  void initState() {
    super.initState();
    _tournamentNameController = TextEditingController();
    _cityController = TextEditingController();
    _gamesPerTeamController = TextEditingController();
    _rulesController = TextEditingController();
    _refundPolicyController = TextEditingController();
    _directorNameController = TextEditingController();
    _directorEmailController = TextEditingController();
    _directorPhoneController = TextEditingController();
    // Initialize controllers for early bird and late fee settings
    _earlyBirdDiscountController = TextEditingController();
    _lateFeeAmountController = TextEditingController();

    // Initialize controllers for field size fees and age group selections
    for (var format in _allPlayFormats) {
      _selectedAgeGroupsByPlayFormat[format] = <String>{}; // Initialize empty sets
      _fieldSizeFeeControllers[format] = TextEditingController(); // One fee per field size
    }

    // Pre-populate director contact info from user profile if available
    try {
      final authState = context.read<AuthBloc>().state;
      if (authState is AuthAuthenticated && authState.userProfile != null) {
        final email = authState.userProfile!.email;
        if (email != null && email.isNotEmpty) {
          _directorEmailController.text = email;
        }

        final firstName = authState.userProfile!.firstName ?? '';
        final lastName = authState.userProfile!.lastName ?? '';
        if (firstName.isNotEmpty || lastName.isNotEmpty) {
          _directorNameController.text = '$firstName $lastName'.trim();
        }

        final phone = authState.userProfile!.phoneNumber;
        if (phone != null && phone.isNotEmpty) {
          _directorPhoneController.text = phone;
        }
      }
    } catch (e) {
      print('Error pre-populating director contact info: $e');
    }

    // Check affiliation status when the screen initializes
    context.read<CreateTournamentBloc>().add(CheckAffiliationStatus());

    final blocState = context.read<CreateTournamentBloc>().state;
    if (blocState is CreateTournamentStep1InProgress) {
      _tournamentNameController.text = blocState.tournament.name;
      _startDate = blocState.tournament.startDate;
      _endDate = blocState.tournament.endDate;

      if (blocState.tournament.city != null) {
        _cityController.text = blocState.tournament.city!;
      }
      if (blocState.tournament.state != null) {
        _selectedState = blocState.tournament.state;
      }
      if (blocState.tournament.tournamentFormat != null) {
        _selectedTournamentFormat = blocState.tournament.tournamentFormat;
      }

      // NEW: Populate divisions from existing tournament data
      // Pre-populate play formats if we have existing divisions (editing or coming back)
      if (blocState.tournament.divisions != null &&
          blocState.tournament.divisions!.isNotEmpty) {
        // Extract play formats, age groups, and fees from existing divisions
        setState(() {
          // Reset first
          _selectedPlayFormats.forEach((key, value) { _selectedPlayFormats[key] = false; });
          _selectedAgeGroupsByPlayFormat.forEach((key, value) { value.clear(); });
          _fieldSizeFeeControllers.forEach((key, controller) { controller.clear(); });

          // Populate from existing divisions
          final Map<String, double> formatFees = {}; // Track fees per format
          for (final division in blocState.tournament.divisions!) {
            if (division.playFormat.isNotEmpty && division.ageGroup.isNotEmpty) {
              // Mark play format as selected
              if (_selectedPlayFormats.containsKey(division.playFormat)) {
                _selectedPlayFormats[division.playFormat] = true;
              }

              // Add age group to the play format
              if (!_selectedAgeGroupsByPlayFormat.containsKey(division.playFormat)) {
                _selectedAgeGroupsByPlayFormat[division.playFormat] = <String>{};
              }
              _selectedAgeGroupsByPlayFormat[division.playFormat]!.add(division.ageGroup);

              // Track fee for this format (use first fee found for each format)
              if (!formatFees.containsKey(division.playFormat)) {
                formatFees[division.playFormat] = division.registrationFee;
              }
            }
          }

          // Set field size fees
          formatFees.forEach((format, fee) {
            if (_fieldSizeFeeControllers.containsKey(format)) {
              _fieldSizeFeeControllers[format]!.text = fee.toStringAsFixed(2);
            }
          });
        });
      }

      if (blocState.tournament.gamesPerTeam != null) {
        _gamesPerTeamController.text =
            blocState.tournament.gamesPerTeam.toString();
      }
      if (blocState.tournament.rules != null) {
        _rulesController.text = blocState.tournament.rules!;
      }
      if (blocState.tournament.refundPolicy != null) {
        _refundPolicyController.text = blocState.tournament.refundPolicy!;
      }
      if (blocState.tournament.directorName != null) {
        _directorNameController.text = blocState.tournament.directorName!;
      }
      if (blocState.tournament.directorEmail != null) {
        _directorEmailController.text = blocState.tournament.directorEmail!;
      }
      if (blocState.tournament.directorPhone != null) {
        _directorPhoneController.text = blocState.tournament.directorPhone!;
      }
      _registrationDeadline = blocState.tournament.registrationDeadline;

      // Populate early bird and late fee settings
      _earlyBirdDeadline = blocState.tournament.earlyBirdDeadline;
      if (blocState.tournament.earlyBirdDiscount != null) {
        _earlyBirdDiscountController.text = blocState.tournament.earlyBirdDiscount!.toString();
      }
      _lateRegistrationStartDate = blocState.tournament.lateRegistrationStartDate;
      if (blocState.tournament.lateFeeAmount != null) {
        _lateFeeAmountController.text = blocState.tournament.lateFeeAmount!.toString();
      }
    }
  }

  @override
  void dispose() {
    _tournamentNameController.dispose();
    _cityController.dispose();
    _gamesPerTeamController.dispose();
    _rulesController.dispose();
    _refundPolicyController.dispose();
    _directorNameController.dispose();
    _directorEmailController.dispose();
    _directorPhoneController.dispose();
    // Dispose field size fee controllers
    _fieldSizeFeeControllers.forEach((_, controller) => controller.dispose());
    _earlyBirdDiscountController.dispose();
    _lateFeeAmountController.dispose();
    super.dispose();
  }

  Future<void> _selectStartDate(BuildContext context, DateTime initialDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: DateTime.now()
          .subtract(const Duration(days: 365)), // Allow past for editing
      lastDate: DateTime(2101),
    );
    if (picked != null && picked != _startDate) {
      setState(() {
        _startDate = picked;
      });
      context.read<CreateTournamentBloc>().add(StartDateChanged(picked));
    }
  }

  Future<void> _selectEndDate(BuildContext context, DateTime initialDate) async {
    final firstDate = _startDate ?? DateTime.now();
    // Make sure initialDate is not before firstDate
    final safeInitialDate =
        initialDate.isBefore(firstDate) ? firstDate : initialDate;
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: safeInitialDate,
      firstDate: firstDate,
      lastDate: DateTime(2101),
    );
    if (picked != null && picked != _endDate) {
      setState(() {
        _endDate = picked;
      });
      context.read<CreateTournamentBloc>().add(EndDateChanged(picked));
    }
  }

  Future<void> _selectRegistrationDeadline(
      BuildContext context, DateTime initialDate) async {
    // Make sure initialDate is not after lastDate
    final lastDate = _startDate ?? DateTime(2101);
    final safeInitialDate = initialDate.isAfter(lastDate) ? lastDate : initialDate;
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: safeInitialDate,
      firstDate: DateTime.now(),
      lastDate: lastDate,
    );
    if (picked != null && picked != _registrationDeadline) {
      setState(() {
        _registrationDeadline = picked;
      });
    }
  }

  Future<void> _selectEarlyBirdDeadline(BuildContext context, DateTime initialDate) async {
    // Make sure initialDate is not in the past
    final safeInitialDate = initialDate.isBefore(DateTime.now()) ? DateTime.now() : initialDate;

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: safeInitialDate,
      firstDate: DateTime.now(),
      lastDate: DateTime(2101),
    );
    if (picked != null && picked != _earlyBirdDeadline) {
      setState(() {
        _earlyBirdDeadline = picked;
      });
    }
  }

  Future<void> _selectLateRegistrationStartDate(BuildContext context, DateTime initialDate) async {
    // Make sure initialDate is not in the past
    final safeInitialDate = initialDate.isBefore(DateTime.now()) ? DateTime.now() : initialDate;

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: safeInitialDate,
      firstDate: DateTime.now(),
      lastDate: DateTime(2101),
    );
    if (picked != null && picked != _lateRegistrationStartDate) {
      setState(() {
        _lateRegistrationStartDate = picked;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<CreateTournamentBloc, CreateTournamentState>(
      listener: (context, state) {
        if (state is CreateTournamentDivisionMatrixStep) {
          // Navigates to the Division Matrix Setup screen
          context.go('/create-tournament/division-matrix');
        } else if (state is CreateTournamentVenueFieldSelectionStep) {
          // Fallback navigation for old flow (if still used)
          context.go('/create-tournament/venue-field-selection');
        }

        // Handle tournament format initialization for independent directors
        if (state is CreateTournamentStep1InProgress) {
          if (state.selectedCreationOption == 'independent' && _selectedTournamentFormat != 'Unsanctioned Tournament') {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (mounted) {
                setState(() {
                  _selectedTournamentFormat = 'Unsanctioned Tournament';
                });
              }
            });
          }
        }
        // This screen's fields are automatically updated by the BlocBuilder.
      },
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Create Tournament - Step 1: Basic Info & Divisions'),
          actions: [
            IconButton(
              icon: const Icon(Icons.logout),
              onPressed: () {
                context.read<AuthBloc>().add(AuthSignOutRequested());
              },
              tooltip: 'Sign Out',
            ),
          ],
        ),
        body: BlocBuilder<CreateTournamentBloc, CreateTournamentState>(
          builder: (context, state) {
            if (state is! CreateTournamentStep1InProgress) {
              return const Center(child: CircularProgressIndicator());
            }

            final currentTournament = state.tournament;
            // Only update _startDate and _endDate if they are null to avoid overwriting user edits
            _startDate ??= currentTournament.startDate;
            _endDate ??= currentTournament.endDate;

            // Ensure tournament format is properly initialized for independent directors
            if (state.selectedCreationOption == 'independent' && _selectedTournamentFormat != 'Unsanctioned Tournament') {
              _selectedTournamentFormat = 'Unsanctioned Tournament';
            }

            return Padding(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      // Tournament Creation Options
                      if (state.affiliationStatusLoading)
                        const Center(
                            child: Padding(
                          padding: EdgeInsets.symmetric(vertical: 8.0),
                          child: CircularProgressIndicator(),
                        ))
                      else if (state.isAffiliated)
                        ...[
                          Text(
                            'Create Tournament As:',
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                          const SizedBox(height: 8.0),
                          // Independent Director option
                          RadioListTile<String>(
                            title: const Text('As an Independent Director'),
                            value: 'independent',
                            groupValue: state.selectedCreationOption,
                            onChanged: (value) {
                              context.read<CreateTournamentBloc>().add(
                                  const TournamentCreationOptionChanged(
                                      'independent'));
                              // Automatically set tournament format to "Unsanctioned Tournament" for independent directors
                              setState(() {
                                _selectedTournamentFormat = 'Unsanctioned Tournament';
                              });
                              context.read<CreateTournamentBloc>().add(
                                  TournamentFormatChanged('Unsanctioned Tournament'));
                            },
                          ),
                          // Affiliated Club option
                          RadioListTile<String>(
                            title: const Text('As an Affiliated Club'),
                            value: 'affiliated',
                            groupValue: state.selectedCreationOption,
                            onChanged: (value) {
                              context.read<CreateTournamentBloc>().add(
                                  const TournamentCreationOptionChanged(
                                      'affiliated'));
                              // Load affiliated clubs when this option is selected
                              context
                                  .read<CreateTournamentBloc>()
                                  .add(LoadAffiliatedClubs());
                            },
                          ),
                          // Club dropdown (only shown if affiliated option is selected)
                          if (state.selectedCreationOption == 'affiliated') ...[
                            const SizedBox(height: 8.0),
                            if (state.affiliatedClubsLoading)
                              const Center(child: CircularProgressIndicator())
                            else if (state.affiliatedClubs == null ||
                                state.affiliatedClubs!.isEmpty)
                              const Text(
                                  'No affiliated clubs found. Please contact support.')
                            else
                              DropdownButtonFormField<String>(
                                decoration: const InputDecoration(
                                  labelText: 'Select Club',
                                  border: OutlineInputBorder(),
                                ),
                                value: state.selectedAffiliatedClubId,
                                items: state.affiliatedClubs!.map((club) {
                                  return DropdownMenuItem<String>(
                                    value: club.id,
                                    child: Text(club.name),
                                  );
                                }).toList(),
                                onChanged: (value) {
                                  if (value != null) {
                                    context
                                        .read<CreateTournamentBloc>()
                                        .add(AffiliatedClubSelected(value));
                                  }
                                },
                                validator: (value) {
                                  if (state.selectedCreationOption ==
                                          'affiliated' &&
                                      (value == null || value.isEmpty)) {
                                    return 'Please select a club';
                                  }
                                  return null;
                                },
                              ),
                          ],
                          const SizedBox(height: 16.0),
                        ],

                      // Tournament Name field
                      TextFormField(
                        controller: _tournamentNameController,
                        decoration: InputDecoration(
                          labelText: 'Tournament Name',
                          errorText: state.validationErrors['name'],
                          border: const OutlineInputBorder(),
                        ),
                        onChanged: (value) {
                          context
                              .read<CreateTournamentBloc>()
                              .add(TournamentNameChanged(value));
                        },
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter tournament name';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16.0),

                      // Tournament Format dropdown - with explicit theme override
                      Theme(
                        data: Theme.of(context).copyWith(
                          inputDecorationTheme: Theme.of(context).inputDecorationTheme.copyWith(
                            filled: false, // Override global filled setting
                            fillColor: Colors.transparent, // Override global fill color
                          ),
                        ),
                        child: DropdownButtonFormField<String>(
                          value: state.selectedCreationOption == 'independent'
                              ? 'Unsanctioned Tournament'
                              : _selectedTournamentFormat,
                          decoration: InputDecoration(
                            labelText: 'Tournament Format',
                            border: const OutlineInputBorder(),
                            // Explicitly control fill behavior
                            filled: state.selectedCreationOption == 'independent',
                            fillColor: state.selectedCreationOption == 'independent'
                                ? Colors.grey[200]
                                : null,
                          ),
                        items: state.selectedCreationOption == 'independent'
                            ? [const DropdownMenuItem<String>(
                                value: 'Unsanctioned Tournament',
                                child: Text('Unsanctioned Tournament'),
                              )]
                            : _tournamentFormats.map((String format) {
                                return DropdownMenuItem<String>(
                                  value: format,
                                  child: Text(format),
                                );
                              }).toList(),
                        onChanged: state.selectedCreationOption == 'independent'
                            ? null // Disabled for independent directors
                            : (String? newValue) {
                                setState(() {
                                  _selectedTournamentFormat = newValue;
                                });
                                if (newValue != null) {
                                  context
                                      .read<CreateTournamentBloc>()
                                      .add(TournamentFormatChanged(newValue));
                                }
                              },
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please select a tournament format';
                          }
                          return null;
                        },
                        ),
                      ),
                      const SizedBox(height: 16.0),

                      // City field
                      TextFormField(
                        controller: _cityController,
                        decoration: const InputDecoration(
                          labelText: 'City',
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter city';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16.0),

                      // State dropdown
                      DropdownButtonFormField<String>(
                        value: _selectedState,
                        decoration: const InputDecoration(
                          labelText: 'State',
                          border: OutlineInputBorder(),
                        ),
                        items: _states.map((String state) {
                          return DropdownMenuItem<String>(
                            value: state,
                            child: Text(state),
                          );
                        }).toList(),
                        onChanged: (String? newValue) {
                          setState(() {
                            _selectedState = newValue;
                          });
                        },
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please select a state';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16.0),

                      // NEW: Offered Play Formats, Age Groups & Fees Section
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Offered Play Formats, Age Groups & Fees',
                            style: Theme.of(context).textTheme.titleLarge,
                          ),
                          const SizedBox(height: 4.0),
                          const Text(
                            'Select play formats, then choose age groups for each and set their registration fees.',
                            style: TextStyle(fontSize: 14, color: Colors.grey),
                          ),
                          const SizedBox(height: 12.0),
                          // Loop through All Play Formats to offer them
                          ..._allPlayFormats.map((format) {
                            return Card(
                              elevation: 1.0,
                              margin: const EdgeInsets.symmetric(vertical: 8.0),
                              child: Column(
                                children: [
                                  CheckboxListTile(
                                    title: Text('Offer $format Format', style: const TextStyle(fontWeight: FontWeight.bold)),
                                    value: _selectedPlayFormats[format],
                                    onChanged: (bool? value) {
                                      setState(() {
                                        _selectedPlayFormats[format] = value ?? false;
                                        if (!(value ?? false)) {
                                          // Clear selected ages and fee for this format if deselected
                                          _selectedAgeGroupsByPlayFormat[format]?.clear();
                                          _fieldSizeFeeControllers[format]?.clear();
                                        }
                                      });
                                    },
                                    controlAffinity: ListTileControlAffinity.leading,
                                  ),
                                  if (_selectedPlayFormats[format] == true)
                                    Padding(
                                      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          // Fee input for this field size
                                          Padding(
                                            padding: const EdgeInsets.only(bottom: 16.0),
                                            child: TextFormField(
                                              controller: _fieldSizeFeeControllers[format],
                                              decoration: InputDecoration(
                                                labelText: 'Registration Fee for $format Format (\$)*',
                                                prefixText: '\$',
                                                border: const OutlineInputBorder(),
                                                isDense: true,
                                                helperText: 'This fee applies to all age groups in this format',
                                              ),
                                              keyboardType: const TextInputType.numberWithOptions(decimal: true),
                                              validator: (value) {
                                                if (value == null || value.isEmpty) {
                                                  return 'Fee is required for selected format';
                                                }
                                                final fee = double.tryParse(value);
                                                if (fee == null || fee < 0) {
                                                  return 'Invalid fee (must be >= 0)';
                                                }
                                                return null;
                                              },
                                              autovalidateMode: AutovalidateMode.onUserInteraction,
                                            ),
                                          ),
                                          const Text('Select Age Groups for this Format:', style: TextStyle(fontWeight: FontWeight.w500)),
                                          const SizedBox(height: 8.0),
                                          Wrap( // Use Wrap for a more compact layout of age group checkboxes
                                            spacing: 8.0,
                                            runSpacing: 4.0,
                                            children: _allAgeGroups.map((ageGroup) {
                                              bool isAgeGroupSelectedForCurrentFormat = _selectedAgeGroupsByPlayFormat[format]?.contains(ageGroup) ?? false;

                                              // Check if this age group is already selected in another active play format
                                              bool isGloballySelectedInOtherFormat = false;

                                              _selectedAgeGroupsByPlayFormat.forEach((otherFormat, selectedAgesInOtherFormat) {
                                                if (otherFormat != format && // Not the current play format we are rendering for
                                                    _selectedPlayFormats[otherFormat] == true && // And that other format is active
                                                    selectedAgesInOtherFormat.contains(ageGroup)) { // And it contains the current age group
                                                  isGloballySelectedInOtherFormat = true;
                                                }
                                              });

                                              return SizedBox(
                                                width: 120, // Adjust width as needed
                                                child: CheckboxListTile(
                                                  title: Text(
                                                    ageGroup,
                                                    style: TextStyle(
                                                      fontSize: 14,
                                                      color: isGloballySelectedInOtherFormat ? Colors.grey : null,
                                                    ),
                                                  ),
                                                  value: isAgeGroupSelectedForCurrentFormat,
                                                  onChanged: isGloballySelectedInOtherFormat
                                                      ? null // Disable if selected in another format
                                                      : (bool? value) {
                                                          if (value == true) {
                                                            // Double-check before allowing selection
                                                            bool alreadySelectedElsewhere = false;
                                                            String? conflictingFormatForSnackbar;
                                                            _selectedAgeGroupsByPlayFormat.forEach((otherFormat, selectedAgesInOtherFormat) {
                                                              if (otherFormat != format &&
                                                                  _selectedPlayFormats[otherFormat] == true &&
                                                                  selectedAgesInOtherFormat.contains(ageGroup)) {
                                                                alreadySelectedElsewhere = true;
                                                                conflictingFormatForSnackbar = otherFormat;
                                                              }
                                                            });

                                                            if (alreadySelectedElsewhere) {
                                                              ScaffoldMessenger.of(context).showSnackBar(
                                                                SnackBar(
                                                                  content: Text('$ageGroup is already offered under $conflictingFormatForSnackbar. Please deselect it there first.'),
                                                                ),
                                                              );
                                                              return; // Prevent selection
                                                            }

                                                            // If not selected elsewhere, proceed to add it
                                                            setState(() {
                                                              _selectedAgeGroupsByPlayFormat[format]?.add(ageGroup);
                                                            });
                                                          } else {
                                                            // If deselecting, allow it
                                                            setState(() {
                                                              _selectedAgeGroupsByPlayFormat[format]?.remove(ageGroup);
                                                            });
                                                          }
                                                        },
                                                  controlAffinity: ListTileControlAffinity.leading,
                                                  dense: true,
                                                  contentPadding: EdgeInsets.zero,
                                                  activeColor: isGloballySelectedInOtherFormat ? Colors.grey : null, // Visual cue
                                                ),
                                              );
                                            }).toList(),
                                          ),
                                        ],
                                      ),
                                    ),
                                ],
                              ),
                            );
                          }).toList(),
                        ],
                      ),
                      const SizedBox(height: 16.0),

                      // Early Bird and Late Fee Settings Section
                      Container(
                        margin: const EdgeInsets.only(top: 16.0, bottom: 8.0),
                        child: Text(
                          'Registration Fee Settings',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                      ),
                      Card(
                        elevation: 2.0,
                        margin: const EdgeInsets.only(bottom: 16.0),
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Early Bird Deadline
                              Row(
                                children: <Widget>[
                                  Expanded(
                                    child: Text(
                                      _earlyBirdDeadline == null
                                          ? 'No early bird deadline set'
                                          : 'Early Bird Deadline: ${DateFormat.yMMMd().format(_earlyBirdDeadline!)}',
                                    ),
                                  ),
                                  ElevatedButton.icon(
                                    icon: const Icon(Icons.calendar_today),
                                    onPressed: () => _selectEarlyBirdDeadline(
                                        context,
                                        _earlyBirdDeadline ?? DateTime.now().add(const Duration(days: 14))),
                                    label: const Text('Select'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Theme.of(context).primaryColor,
                                      foregroundColor: Colors.white,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 16.0),
                              // Early Bird Discount
                              TextFormField(
                                controller: _earlyBirdDiscountController,
                                decoration: const InputDecoration(
                                  labelText: 'Early Bird Discount (\$)',
                                  border: OutlineInputBorder(),
                                  prefixIcon: Icon(Icons.attach_money),
                                  hintText: 'Amount to discount from base fee',
                                ),
                                keyboardType: TextInputType.number,
                                validator: (value) {
                                  if (value != null && value.isNotEmpty) {
                                    final parsedValue = double.tryParse(value);
                                    if (parsedValue == null || parsedValue < 0) {
                                      return 'Enter a valid discount (>=0)';
                                    }
                                  }
                                  return null;
                                },
                              ),
                              const SizedBox(height: 16.0),
                              // Late Registration Start Date
                              Row(
                                children: <Widget>[
                                  Expanded(
                                    child: Text(
                                      _lateRegistrationStartDate == null
                                          ? 'No late registration start date set'
                                          : 'Late Registration Starts: ${DateFormat.yMMMd().format(_lateRegistrationStartDate!)}',
                                    ),
                                  ),
                                  ElevatedButton.icon(
                                    icon: const Icon(Icons.calendar_today),
                                    onPressed: () => _selectLateRegistrationStartDate(
                                        context,
                                        _lateRegistrationStartDate ?? DateTime.now().add(const Duration(days: 30))),
                                    label: const Text('Select'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Theme.of(context).primaryColor,
                                      foregroundColor: Colors.white,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 16.0),
                              // Late Fee Amount
                              TextFormField(
                                controller: _lateFeeAmountController,
                                decoration: const InputDecoration(
                                  labelText: 'Late Registration Fee (\$)',
                                  border: OutlineInputBorder(),
                                  prefixIcon: Icon(Icons.attach_money),
                                  hintText: 'Additional fee for late registration',
                                ),
                                keyboardType: TextInputType.number,
                                validator: (value) {
                                  if (value != null && value.isNotEmpty) {
                                    final parsedValue = double.tryParse(value);
                                    if (parsedValue == null || parsedValue < 0) {
                                      return 'Enter a valid fee (>=0)';
                                    }
                                  }
                                  return null;
                                },
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 16.0),

                      // Games Per Team field (now global tournament average/goal)
                      TextFormField(
                        controller: _gamesPerTeamController,
                        decoration: const InputDecoration(
                          labelText:
                              'Games Per Team (Overall Tournament Average/Goal)',
                        ),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter games per team';
                          }
                          final parsedValue = int.tryParse(value);
                          if (parsedValue == null) {
                            return 'Please enter a valid number';
                          }
                          if (parsedValue <= 0) {
                            return 'Must be positive';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16.0),

                      // Rules field
                      TextFormField(
                        controller: _rulesController,
                        decoration: const InputDecoration(
                          labelText: 'Tournament Rules',
                          border: OutlineInputBorder(),
                          hintText: 'Enter tournament rules and regulations',
                        ),
                        maxLines: 4,
                        validator: (value) {
                          return null; // Rules are optional
                        },
                      ),
                      const SizedBox(height: 16.0),

                      // Refund Policy field
                      TextFormField(
                        controller: _refundPolicyController,
                        decoration: const InputDecoration(
                          labelText: 'Refund Policy',
                          border: OutlineInputBorder(),
                          hintText: 'Enter tournament refund policy',
                        ),
                        maxLines: 4,
                        validator: (value) {
                          return null; // Refund policy is optional
                        },
                      ),
                      const SizedBox(height: 24.0),

                      // Date Selection Section
                      Container(
                        margin: const EdgeInsets.only(top: 16.0, bottom: 8.0),
                        child: Text(
                          'Tournament Dates',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                      ),
                      // Date fields in a card for better visibility
                      Card(
                        elevation: 2.0,
                        margin: const EdgeInsets.only(bottom: 16.0),
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Start Date
                              Row(
                                children: <Widget>[
                                  Expanded(
                                    child: Text(
                                      _startDate == null
                                          ? 'No start date selected'
                                          : 'Start Date: ${DateFormat.yMMMd().format(_startDate!.toLocal())}',
                                      style: TextStyle(
                                        color: state.validationErrors[
                                                    'startDate'] !=
                                                null
                                            ? Theme.of(context)
                                                .colorScheme
                                                .error
                                            : null,
                                      ),
                                    ),
                                  ),
                                  ElevatedButton.icon(
                                    icon: const Icon(Icons.calendar_today),
                                    onPressed: () => _selectStartDate(
                                        context,
                                        _startDate ??
                                            currentTournament.startDate),
                                    label: const Text('Select'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor:
                                          Theme.of(context).primaryColor,
                                      foregroundColor: Colors.white,
                                    ),
                                  ),
                                ],
                              ),
                              if (state.validationErrors['startDate'] != null)
                                Padding(
                                  padding: const EdgeInsets.only(
                                      top: 4.0, left: 12.0),
                                  child: Text(
                                    state.validationErrors['startDate']!,
                                    style: TextStyle(
                                        color: Theme.of(context)
                                            .colorScheme
                                            .error,
                                        fontSize: 12),
                                  ),
                                ),
                              const SizedBox(height: 16.0),
                              // End Date
                              Row(
                                children: <Widget>[
                                  Expanded(
                                    child: Text(
                                      _endDate == null
                                          ? 'No end date selected'
                                          : 'End Date: ${DateFormat.yMMMd().format(_endDate!.toLocal())}',
                                      style: TextStyle(
                                        color:
                                            state.validationErrors['endDate'] !=
                                                    null
                                                ? Theme.of(context)
                                                    .colorScheme
                                                    .error
                                                : null,
                                      ),
                                    ),
                                  ),
                                  ElevatedButton.icon(
                                    icon: const Icon(Icons.calendar_today),
                                    onPressed: () => _selectEndDate(
                                        context,
                                        _endDate ?? currentTournament.endDate),
                                    label: const Text('Select'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor:
                                          Theme.of(context).primaryColor,
                                      foregroundColor: Colors.white,
                                    ),
                                  ),
                                ],
                              ),
                              if (state.validationErrors['endDate'] != null)
                                Padding(
                                  padding: const EdgeInsets.only(
                                      top: 4.0, left: 12.0),
                                  child: Text(
                                    state.validationErrors['endDate']!,
                                    style: TextStyle(
                                        color: Theme.of(context)
                                            .colorScheme
                                            .error,
                                        fontSize: 12),
                                  ),
                                ),
                              const SizedBox(height: 16.0),

                              // Registration Deadline
                              Row(
                                children: <Widget>[
                                  Expanded(
                                    child: Text(
                                      _registrationDeadline == null
                                          ? 'No registration deadline selected'
                                          : 'Registration Deadline: ${DateFormat.yMMMd().format(_registrationDeadline!.toLocal())}',
                                    ),
                                  ),
                                  ElevatedButton.icon(
                                    icon: const Icon(Icons.calendar_today),
                                    onPressed: () =>
                                        _selectRegistrationDeadline(
                                            context,
                                            _registrationDeadline ??
                                                DateTime.now().add(
                                                    const Duration(days: 30))),
                                    label: const Text('Select'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor:
                                          Theme.of(context).primaryColor,
                                      foregroundColor: Colors.white,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 24.0),

                      // Director Contact Information Section
                      Container(
                        margin: const EdgeInsets.only(top: 16.0, bottom: 8.0),
                        child: Text(
                          'Tournament Director Contact Information',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                      ),
                      // Director contact fields in a card for better visibility
                      Card(
                        elevation: 2.0,
                        margin: const EdgeInsets.only(bottom: 16.0),
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Director Name field
                              TextFormField(
                                controller: _directorNameController,
                                decoration: const InputDecoration(
                                  labelText: 'Director Name',
                                  border: OutlineInputBorder(),
                                ),
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Please enter director name';
                                  }
                                  return null;
                                },
                              ),
                              const SizedBox(height: 16.0),
                              // Director Email field
                              TextFormField(
                                controller: _directorEmailController,
                                decoration: const InputDecoration(
                                  labelText: 'Director Email',
                                  border: OutlineInputBorder(),
                                ),
                                keyboardType: TextInputType.emailAddress,
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Please enter director email';
                                  }
                                  if (!value.contains('@') ||
                                      !value.contains('.')) {
                                    return 'Please enter a valid email address';
                                  }
                                  return null;
                                },
                              ),
                              const SizedBox(height: 16.0),
                              // Director Phone field
                              TextFormField(
                                controller: _directorPhoneController,
                                decoration: const InputDecoration(
                                  labelText: 'Director Phone',
                                  border: OutlineInputBorder(),
                                ),
                                keyboardType: TextInputType.phone,
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Please enter director phone number';
                                  }
                                  return null;
                                },
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 24.0),

                      // Navigation buttons
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          OutlinedButton(
                            onPressed: () {
                              context.go('/director-dashboard');
                            },
                            child: const Text('Back to Dashboard'),
                          ),
                          ElevatedButton(
                            onPressed: () {
                              if (_formKey.currentState!.validate()) {
                                if (_startDate == null) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                        content:
                                            Text('Please select a start date.')),
                                  );
                                  return;
                                }
                                if (_endDate == null) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                        content:
                                            Text('Please select an end date.')),
                                  );
                                  return;
                                }

                                final int? gamesPerTeam =
                                    int.tryParse(_gamesPerTeamController.text);

                                final double? earlyBirdDiscount = _earlyBirdDiscountController.text.isNotEmpty
                                    ? double.tryParse(_earlyBirdDiscountController.text)
                                    : null;
                                final double? lateFeeAmount = _lateFeeAmountController.text.isNotEmpty
                                    ? double.tryParse(_lateFeeAmountController.text)
                                    : null;

                                // Get selected play formats with age groups and their fees
                                final Map<String, Map<String, double>> ageGroupFeesByFieldSize = {};
                                bool atLeastOnePlayFormatSelected = false;
                                bool allFeesValidForSelected = true;

                                _selectedPlayFormats.forEach((format, isSelected) {
                                  if (isSelected) {
                                    atLeastOnePlayFormatSelected = true;

                                    // Check if at least one age group is selected for this format
                                    bool currentFormatHasSelectedAgeGroup = _selectedAgeGroupsByPlayFormat[format]?.isNotEmpty ?? false;
                                    if (!currentFormatHasSelectedAgeGroup) {
                                        // If a play format is selected, at least one age group under it must be selected
                                        allFeesValidForSelected = false;
                                        ScaffoldMessenger.of(context).showSnackBar(
                                          SnackBar(content: Text('Please select at least one age group for the $format format.'))
                                        );
                                        return;
                                    }

                                    // Check if fee is valid for this format
                                    final feeController = _fieldSizeFeeControllers[format];
                                    final fee = double.tryParse(feeController?.text ?? '');
                                    if (fee == null || fee < 0) {
                                      allFeesValidForSelected = false;
                                      return;
                                    }

                                    // Create age group fees map with the same fee for all selected age groups
                                    final Map<String, double> ageGroupFees = {};
                                    _selectedAgeGroupsByPlayFormat[format]?.forEach((ageGroup) {
                                      ageGroupFees[ageGroup] = fee;
                                    });

                                    if (ageGroupFees.isNotEmpty) {
                                      ageGroupFeesByFieldSize[format] = ageGroupFees;
                                    }
                                  }
                                });

                                if (!atLeastOnePlayFormatSelected) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(content: Text('Please select at least one Play Format to offer.'))
                                  );
                                  return;
                                }
                                if (!allFeesValidForSelected) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(content: Text('Please enter a valid fee for all selected age groups under the offered play formats.'))
                                  );
                                  return;
                                }

                                // Validate that at least one age group is selected for the play formats
                                bool hasAgeGroupsSelected = false;
                                for (final format in _selectedPlayFormats.keys) {
                                  if (_selectedPlayFormats[format] == true) {
                                    final selectedAgeGroups = _selectedAgeGroupsByPlayFormat[format];
                                    if (selectedAgeGroups != null && selectedAgeGroups.isNotEmpty) {
                                      hasAgeGroupsSelected = true;
                                      break;
                                    }
                                  }
                                }
                                if (!hasAgeGroupsSelected) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(content: Text('Please select at least one age group for your play formats. This is needed to create divisions in the next step.'))
                                  );
                                  return;
                                }

                                if (state.selectedCreationOption ==
                                        'affiliated' &&
                                    (state.selectedAffiliatedClubId == null ||
                                        state.selectedAffiliatedClubId!
                                            .isEmpty)) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                        content: Text(
                                            'Please select an affiliated club.')),
                                  );
                                  return;
                                }
                                if (_selectedState == null) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                        content: Text('Please select a state.')),
                                  );
                                  return;
                                }
                                if (_selectedTournamentFormat == null) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                        content: Text(
                                            'Please select a tournament format.')),
                                  );
                                  return;
                                }

                                // Ensure correct tournament format for independent directors
                                final bool isIndependentDirectorFlow = state.selectedCreationOption == 'independent';
                                final String finalTournamentFormat = isIndependentDirectorFlow
                                    ? 'Unsanctioned Tournament'
                                    : _selectedTournamentFormat!;

                                final tournamentDetails = Tournament(
                                  name: _tournamentNameController.text,
                                  sportType:
                                      'Soccer', // Default to Soccer
                                  startDate: _startDate!,
                                  endDate: _endDate!,
                                  status: currentTournament.status,
                                  city: _cityController.text,
                                  state: _selectedState,
                                  registrationDeadline: _registrationDeadline,
                                  divisions: const [], // Divisions will be set in the NEXT step
                                  gamesPerTeam: gamesPerTeam,
                                  managingClubId:
                                      state.selectedCreationOption ==
                                              'affiliated'
                                          ? state.selectedAffiliatedClubId
                                          : null,
                                  rules: _rulesController.text.isNotEmpty
                                      ? _rulesController.text
                                      : null,
                                  refundPolicy:
                                      _refundPolicyController.text.isNotEmpty
                                          ? _refundPolicyController.text
                                          : null,
                                  directorName: _directorNameController.text,
                                  directorEmail: _directorEmailController.text,
                                  directorPhone: _directorPhoneController.text,
                                  tournamentFormat: finalTournamentFormat,
                                  // Early bird and late fee settings
                                  earlyBirdDeadline: _earlyBirdDeadline,
                                  earlyBirdDiscount: earlyBirdDiscount,
                                  lateRegistrationStartDate: _lateRegistrationStartDate,
                                  lateFeeAmount: lateFeeAmount,
                                );
                                // Use the new Step1CoreInfoCompleted event for matrix approach
                                context
                                    .read<CreateTournamentBloc>()
                                    .add(Step1CoreInfoCompleted(
                                      tournamentDetails: tournamentDetails,
                                      selectedAgeGroupFeesByPlayFormat: ageGroupFeesByFieldSize,
                                    ));
                              }
                            },
                            child: const Text('Next: Define Divisions Matrix'),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}