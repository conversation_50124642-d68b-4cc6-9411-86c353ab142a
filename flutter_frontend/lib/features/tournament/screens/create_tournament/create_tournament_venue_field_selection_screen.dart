// soccer_frontend/features/tournament/presentation/screens/create_tournament_venue_field_selection_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:soccer_frontend/data/models/field.dart';
import 'package:soccer_frontend/data/models/venue.dart';
import 'package:soccer_frontend/features/auth/blocs/auth_bloc.dart';
import 'package:soccer_frontend/features/tournament/blocs/create_tournament/create_tournament_bloc.dart';
import 'package:soccer_frontend/features/tournament/blocs/create_tournament/create_tournament_event.dart';
import 'package:soccer_frontend/features/tournament/blocs/create_tournament/create_tournament_state.dart';

class CreateTournamentVenueFieldSelectionScreen extends StatelessWidget {
  const CreateTournamentVenueFieldSelectionScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocListener<CreateTournamentBloc, CreateTournamentState>(
      listenWhen: (previous, current) {
        // Only navigate when transitioning *from this step* to the *specific next step*.
        return previous is CreateTournamentVenueFieldSelectionStep &&
               current is CreateTournamentGameTimingConfigStep;
      },
      listener: (context, state) {
        if (state is CreateTournamentGameTimingConfigStep) {
          print('DEBUG: Navigating to game timing from VenueFieldSelectionScreen BlocListener (REVISED)');
          context.go('/create-tournament/game-timing');
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Select Venues & Fields'),
          actions: [
            IconButton(
              icon: const Icon(Icons.logout),
              onPressed: () {
                context.read<AuthBloc>().add(AuthSignOutRequested());
              },
              tooltip: 'Sign Out',
            ),
          ],
        ),
        body: BlocBuilder<CreateTournamentBloc, CreateTournamentState>(
          buildWhen: (previous, current) {
            // Force rebuild when loading states change
            if (previous is CreateTournamentVenueFieldSelectionStep &&
                current is CreateTournamentVenueFieldSelectionStep) {
              final loadingChanged =
                  previous.fieldsLoadingByVenueId != current.fieldsLoadingByVenueId;
              final fieldsChanged =
                  previous.availableFieldsByVenueId != current.availableFieldsByVenueId;
              final selectedIdsChanged =
                  previous.selectedVenueIds != current.selectedVenueIds ||
                      previous.selectedFieldIds != current.selectedFieldIds;
              if (loadingChanged || fieldsChanged || selectedIdsChanged) {
                print(
                    'DEBUG: BlocBuilder rebuilding - loadingChanged: $loadingChanged, fieldsChanged: $fieldsChanged, selectedIdsChanged: $selectedIdsChanged');
              }
              return true; // Always rebuild for this state type to reflect selections/loading
            }
            return true; // Rebuild on any state change that impacts this screen
          },
          builder: (context, state) {
            if (state is! CreateTournamentVenueFieldSelectionStep) {
              return const Center(child: CircularProgressIndicator());
            }

            final venueFieldState = state;
            final venues = venueFieldState.availableVenues ?? [];
            final selectedVenueIds = venueFieldState.selectedVenueIds;
            final selectedFieldIds = venueFieldState.selectedFieldIds;

            // Debug: Check for venues with null IDs (should be assigned by DB after creation)
            for (final venue in venues) {
              if (venue.id == null) {
                print(
                    'DEBUG: Found venue with null ID: ${venue.name} at ${venue.address}');
              }
            }

            return Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Select Venues and Fields',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8.0),
                  const Text(
                    'Choose the venues and fields that will be used for this tournament. Fields from selected venues will determine game timing options.',
                    style: TextStyle(fontSize: 16.0),
                  ),
                  const SizedBox(height: 16.0),

                  // Venues section
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Available Venues',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      ElevatedButton.icon(
                        onPressed: () {
                          // Navigate to add venue screen within tournament creation flow
                          context.go('/create-tournament/venue-form');
                        },
                        icon: const Icon(Icons.add),
                        label: const Text('Add Venue'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8.0),
                  if (venueFieldState.venuesLoading)
                    const Center(child: CircularProgressIndicator())
                  else if (venues.isEmpty)
                    const Padding(
                      padding: EdgeInsets.symmetric(vertical: 16.0),
                      child: Text('No venues available. Please add venues first.'),
                    )
                  else
                    Expanded(
                      child: ListView.builder(
                        itemCount: venues.length,
                        itemBuilder: (context, index) {
                          final venue = venues[index];
                          final isSelected = selectedVenueIds.contains(venue.id);
                          return Card(
                            margin: const EdgeInsets.only(bottom: 8.0),
                            child: ExpansionTile(
                              title: Row(
                                children: [
                                  Expanded(child: Text(venue.name)),
                                  IconButton(
                                    icon: const Icon(Icons.edit, size: 20),
                                    onPressed: venue.id != null
                                        ? () {
                                            // Navigate to edit venue
                                            context.go(
                                                '/create-tournament/venue-form',
                                                extra: venue);
                                          }
                                        : null,
                                    tooltip: 'Edit Venue',
                                  ),
                                ],
                              ),
                              subtitle: Text(
                                  '${venue.address}, ${venue.city}, ${venue.state}'),
                              leading: Checkbox(
                                value: isSelected,
                                onChanged: (value) {
                                  print(
                                      'DEBUG: Venue checkbox clicked - ID: ${venue.id}, Name: ${venue.name}, Value: $value');
                                  print(
                                      'DEBUG: Current selected venues: ${venueFieldState.selectedVenueIds}');
                                  context.read<CreateTournamentBloc>().add(
                                      ToggleVenueSelection(venue.id ??
                                          '')); // Use null-aware operator

                                  // If venue is being selected and fields aren't loaded, load them
                                  if (value == true &&
                                      venue.id != null &&
                                      !(venueFieldState
                                              .availableFieldsByVenueId
                                              .containsKey(venue.id) &&
                                          (venueFieldState
                                                  .availableFieldsByVenueId[
                                                      venue.id]
                                                  ?.isNotEmpty ??
                                              false))) {
                                    // Check if list is empty as well
                                    print(
                                        'DEBUG: Triggering field load for venue: ${venue.id}');
                                    context
                                        .read<CreateTournamentBloc>()
                                        .add(LoadFieldsForVenue(venue.id!));
                                  } else {
                                    print(
                                        'DEBUG: Not loading fields - value: $value, venue.id: ${venue.id}, fields already loaded: ${venueFieldState.availableFieldsByVenueId.containsKey(venue.id)}');
                                  }
                                },
                              ),
                              children: [
                                // Always show fields when expanded (not dependent on selection)
                                const Padding(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: 16.0, vertical: 8.0),
                                  child: Align(
                                    alignment: Alignment.centerLeft,
                                    child: Text(
                                      'Fields',
                                      style: TextStyle(
                                          fontWeight: FontWeight.bold),
                                    ),
                                  ),
                                ),
                                if (venueFieldState
                                        .fieldsLoadingByVenueId[venue.id] ==
                                    true) ...[
                                  Builder(builder: (context) {
                                    print(
                                        'DEBUG: Showing loading spinner for venue ${venue.id}');
                                    return const SizedBox.shrink();
                                  }),
                                  const Padding(
                                    padding: EdgeInsets.all(16.0),
                                    child:
                                        Center(child: CircularProgressIndicator()),
                                  ),
                                ],
                                if (venueFieldState
                                        .fieldsLoadingByVenueId[venue.id] !=
                                    true) ...[
                                  Builder(builder: (context) {
                                    print(
                                        'DEBUG: NOT showing loading spinner for venue ${venue.id}, loading state: ${venueFieldState.fieldsLoadingByVenueId[venue.id]}');
                                    return const SizedBox.shrink();
                                  }),
                                  if (venueFieldState
                                          .availableFieldsByVenueId[venue.id]
                                          ?.isEmpty ??
                                      true)
                                    Padding(
                                      padding: const EdgeInsets.all(16.0),
                                      child: Column(
                                        children: [
                                          const Text(
                                              'No fields available for this venue.'),
                                          const SizedBox(height: 8.0),
                                          ElevatedButton(
                                            onPressed: venue.id != null
                                                ? () {
                                                    // Navigate to field creation form within tournament context
                                                    context.go(
                                                        '/create-tournament/venues/${venue.id}/fields/add');
                                                  }
                                                : null,
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor: venue.id != null
                                                  ? Colors.green
                                                  : Colors.grey,
                                              foregroundColor: Colors.white,
                                            ),
                                            child: Text(venue.id != null
                                                ? 'Add Field'
                                                : 'Invalid Venue'),
                                          ),
                                        ],
                                      ),
                                    )
                                  else
                                    Column(
                                      children: [
                                        ListView.builder(
                                          shrinkWrap: true,
                                          physics:
                                              const NeverScrollableScrollPhysics(),
                                          itemCount: venueFieldState
                                                  .availableFieldsByVenueId[
                                                      venue.id]
                                                  ?.length ??
                                              0,
                                          itemBuilder: (context, fieldIndex) {
                                            final field = venueFieldState
                                                    .availableFieldsByVenueId[
                                                venue.id]![fieldIndex];
                                            final isFieldSelected =
                                                selectedFieldIds
                                                    .contains(field.id);
                                            return ListTile(
                                              title: Text(field.nameOrNumber),
                                              subtitle: Text(
                                                  '${field.locationType ?? 'N/A'} • Size: ${field.size ?? 'N/A'} • Type: ${field.surfaceType ?? 'N/A'}'),
                                              leading: Checkbox(
                                                value: isFieldSelected,
                                                onChanged: isSelected
                                                    ? (value) {
                                                        // Only allow field selection if venue is selected
                                                        context
                                                            .read<
                                                                CreateTournamentBloc>()
                                                            .add(ToggleFieldSelection(
                                                                field.id ??
                                                                    '')); // Use null-aware operator
                                                      }
                                                    : null, // Disable field selection if venue not selected
                                              ),
                                              trailing: IconButton(
                                                icon: const Icon(Icons.edit,
                                                    size: 20),
                                                onPressed: field.id != null
                                                    ? () {
                                                        // Navigate to edit field
                                                        context.go(
                                                            '/create-tournament/venues/${venue.id}/fields/add',
                                                            extra: field);
                                                      }
                                                    : null,
                                                tooltip: 'Edit Field',
                                              ),
                                            );
                                          },
                                        ),
                                        Padding(
                                          padding: const EdgeInsets.all(8.0),
                                          child: ElevatedButton(
                                            onPressed: venue.id != null
                                                ? () {
                                                    // Navigate to field creation form within tournament context
                                                    context.go(
                                                        '/create-tournament/venues/${venue.id}/fields/add');
                                                  }
                                                : null,
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor: venue.id != null
                                                  ? Colors.green
                                                  : Colors.grey,
                                              foregroundColor: Colors.white,
                                            ),
                                            child: Text(venue.id != null
                                                ? 'Add Another Field'
                                                : 'Invalid Venue'),
                                          ),
                                        ),
                                      ],
                                    ),
                                ],
                              ],
                              onExpansionChanged: (expanded) {
                                // Load fields when expanded (regardless of selection)
                                if (expanded &&
                                    !(venueFieldState.availableFieldsByVenueId
                                            .containsKey(venue.id) &&
                                        (venueFieldState
                                                .availableFieldsByVenueId[
                                                    venue.id]
                                                ?.isNotEmpty ??
                                            false))) {
                                  print(
                                      'DEBUG: Loading fields for venue ${venue.id} on expansion');
                                  context.read<CreateTournamentBloc>().add(
                                      LoadFieldsForVenue(venue.id ??
                                          '')); // Use null-aware operator
                                }
                              },
                            ),
                          );
                        },
                      ),
                    ),
                  const SizedBox(height: 16.0),

                  // Navigation buttons
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      OutlinedButton(
                        onPressed: () {
                          print('DEBUG: Back button clicked, navigating to division-matrix');
                          context.go('/create-tournament/division-matrix');
                        },
                        child: const Text('Back'),
                      ),
                      ElevatedButton(
                        onPressed: selectedVenueIds.isNotEmpty &&
                                selectedFieldIds
                                    .isNotEmpty // Ensure at least one venue and one field is selected
                            ? () {
                                // Get selected venues (already available in state.availableVenues)
                                final List<Venue> selectedVenuesList = venues
                                    .where(
                                      (venue) =>
                                          selectedVenueIds.contains(venue.id),
                                    )
                                    .toList();

                                // Get selected fields by venue (from state.availableFieldsByVenueId, filtered by selectedFieldIds)
                                final Map<String, List<Field>>
                                    selectedFieldsByVenueIdMap = {};
                                for (final venueId in selectedVenueIds) {
                                  final List<Field> fieldsForVenue =
                                      venueFieldState
                                              .availableFieldsByVenueId[venueId] ??
                                          [];
                                  final List<Field> selectedVenueFields =
                                      fieldsForVenue
                                          .where(
                                            (field) => selectedFieldIds
                                                .contains(field.id),
                                          )
                                          .toList();
                                  if (selectedVenueFields.isNotEmpty) {
                                    selectedFieldsByVenueIdMap[venueId] =
                                        selectedVenueFields;
                                  }
                                }

                                // NEW: Derive selectedAgeGroups and selectedFieldSizes from selected divisions and fields
                                final Set<String> derivedAgeGroups = {};
                                final Set<String> derivedFieldSizes = {};

                                // Populate derivedAgeGroups from the Tournament's divisions (which were set in Step 1)
                                if (venueFieldState.tournament.divisions != null) {
                                  for (final division in venueFieldState
                                      .tournament.divisions!) {
                                    derivedAgeGroups.add(division.ageGroup);
                                  }
                                }
                                // Populate derivedFieldSizes from the actually selected fields
                                for (final entry
                                    in selectedFieldsByVenueIdMap.entries) {
                                  for (final field in entry.value) {
                                    if (field.size != null &&
                                        field.size!.isNotEmpty) {
                                      // Ensure size is not null/empty
                                      derivedFieldSizes.add(field.size!);
                                    }
                                  }
                                }

                                // Dispatch the event to proceed to Game Timing Configuration
                                context.read<CreateTournamentBloc>().add(
                                      ProceedToNextStepFromVenueFieldSelection(
                                        selectedAgeGroups: derivedAgeGroups,
                                        selectedFieldSizes: derivedFieldSizes,
                                        selectedVenues:
                                            selectedVenuesList, // Pass through
                                        selectedFieldsByVenueId:
                                            selectedFieldsByVenueIdMap, // Pass through
                                        selectedFieldIds:
                                            selectedFieldIds, // Pass through
                                      ),
                                    );
                              }
                            : null,
                        child: const Text('Next'),
                      ),
                    ],
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}